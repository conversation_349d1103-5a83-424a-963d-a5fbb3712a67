"""
Test script for AI Mentor agents
Tests the basic functionality of agents and tools
"""

from agents import create_ai_mentor_crew
import sqlite3
import pandas as pd
import os

def test_database_connection():
    """Test basic database connectivity and data"""
    print("=== Testing Database Connection ===\n")

    try:
        conn = sqlite3.connect("university.db")

        # Test students
        print("1. Students in database:")
        students_df = pd.read_sql_query("SELECT id, name, gpa, major, year FROM students", conn)
        print(students_df.to_string(index=False))
        print()

        # Test courses count by category
        print("2. Courses by category:")
        courses_df = pd.read_sql_query("SELECT category, COUNT(*) as count FROM courses GROUP BY category", conn)
        print(courses_df.to_string(index=False))
        print()

        # Test enrollments for Alice
        print("3. Alice's enrollments:")
        enrollments_df = pd.read_sql_query("""
            SELECT c.name, c.credits, c.category, e.grade
            FROM enrollments e
            JOIN courses c ON e.course_id = c.id
            WHERE e.student_id = 1
        """, conn)
        print(enrollments_df.to_string(index=False))
        print()

        # Test degree requirements
        print("4. Degree requirements:")
        requirements_df = pd.read_sql_query("SELECT category, required_credits, description FROM degree_requirements", conn)
        print(requirements_df.to_string(index=False))

        conn.close()
        print("\n✓ Database connection and data verification successful!")

    except Exception as e:
        print(f"✗ Database error: {e}")

    print("\n" + "="*50 + "\n")

def test_crew_creation():
    """Test crew creation (without running full workflow)"""
    print("=== Testing Crew Creation ===\n")
    
    try:
        crew = create_ai_mentor_crew(1)
        print(f"✓ Crew created successfully!")
        print(f"  - Agents: {len(crew.agents)}")
        print(f"  - Tasks: {len(crew.tasks)}")
        
        # Print agent roles
        print("\nAgent roles:")
        for i, agent in enumerate(crew.agents):
            print(f"  {i+1}. {agent.role}")
            
        print("\nTask descriptions:")
        for i, task in enumerate(crew.tasks):
            print(f"  {i+1}. {task.description[:100]}...")
            
    except Exception as e:
        print(f"✗ Error creating crew: {e}")

def test_simple_crew_run():
    """Test a simple crew run (only if API key is available)"""
    print("=== Testing Simple Crew Run ===\n")

    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        print("⚠ DEEPSEEK_API_KEY not found. Skipping crew execution test.")
        print("To test crew execution, please:")
        print("1. Copy .env.template to .env")
        print("2. Add your DeepSeek API key")
        print("3. Run this test again")
        return
    
    try:
        print("Creating crew for Alice (Student ID: 1)...")
        crew = create_ai_mentor_crew(1)
        
        print("Running crew workflow...")
        print("Note: This may take a few minutes with DeepSeek...")
        
        # Run the crew
        result = crew.kickoff()
        
        print("✓ Crew execution completed!")
        print("\nResults:")
        print(result)
        
    except Exception as e:
        print(f"✗ Error running crew: {e}")
        print("This might be due to API configuration or network issues.")

if __name__ == "__main__":
    print("AI Mentor Agent Testing")
    print("=" * 50)

    # Test database first
    test_database_connection()

    # Test crew creation
    test_crew_creation()

    # Test crew execution (if API key available)
    test_simple_crew_run()

    print("\n" + "=" * 50)
    print("Testing complete!")
