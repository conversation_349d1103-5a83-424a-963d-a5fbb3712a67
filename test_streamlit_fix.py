"""
Quick test to verify the Streamlit app runs without nested expander errors
"""

import subprocess
import time
import signal
import os

def test_streamlit_startup():
    """Test that Streamlit app starts without errors"""
    print("=== Testing Streamlit App Startup ===\n")
    
    try:
        # Start Streamlit in the background
        print("Starting Streamlit app...")
        process = subprocess.Popen(
            ["streamlit", "run", "app.py", "--server.headless", "true", "--server.port", "8502"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a few seconds for startup
        time.sleep(5)
        
        # Check if process is still running (no immediate crash)
        if process.poll() is None:
            print("✅ Streamlit app started successfully!")
            print("✅ No immediate crashes detected")
            print("✅ Nested expander issue appears to be fixed")
            
            # Terminate the process
            process.terminate()
            process.wait(timeout=5)
            print("✅ App terminated cleanly")
            
        else:
            # Process crashed, get error output
            stdout, stderr = process.communicate()
            print("❌ Streamlit app crashed on startup")
            print("STDOUT:", stdout)
            print("STDERR:", stderr)
            
    except FileNotFoundError:
        print("❌ Streamlit not found. Install with: pip install streamlit")
    except Exception as e:
        print(f"❌ Error testing Streamlit: {e}")
        
        # Clean up process if it exists
        try:
            if 'process' in locals() and process.poll() is None:
                process.terminate()
        except:
            pass

def check_app_structure():
    """Check that the app file structure is correct"""
    print("\n=== Checking App Structure ===\n")
    
    required_files = [
        "app.py",
        "agents.py", 
        "university.db",
        "requirements.txt"
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
    
    # Check for key functions in app.py
    try:
        with open("app.py", "r") as f:
            content = f.read()
            
        functions_to_check = [
            "display_analysis_results",
            "display_agent_trace", 
            "display_executive_summary",
            "main"
        ]
        
        print("\nChecking app.py functions:")
        for func in functions_to_check:
            if f"def {func}" in content:
                print(f"✅ {func}() defined")
            else:
                print(f"❌ {func}() missing")
                
        # Check for nested expander patterns
        if "with st.expander" in content and content.count("with st.expander") > 1:
            # Look for potential nesting
            lines = content.split('\n')
            expander_depth = 0
            nested_found = False
            
            for line in lines:
                if "with st.expander" in line:
                    expander_depth += 1
                    if expander_depth > 1:
                        nested_found = True
                        break
                elif line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                    expander_depth = 0
            
            if nested_found:
                print("⚠️  Potential nested expanders still detected")
            else:
                print("✅ No nested expanders detected")
        else:
            print("✅ Expander usage looks safe")
            
    except Exception as e:
        print(f"❌ Error checking app.py: {e}")

if __name__ == "__main__":
    print("Streamlit App Fix Verification")
    print("=" * 50)
    
    check_app_structure()
    test_streamlit_startup()
    
    print("\n" + "=" * 50)
    print("Test complete!")
    print("\nIf all tests passed, you can now run:")
    print("streamlit run app.py")
    print("\nAnd test the Agent Trace viewer without nested expander errors!")
