"""
Test markdown rendering in the Streamlit app
"""

import streamlit as st

def test_markdown_content():
    """Test various markdown elements"""
    
    st.title("🧪 Markdown Rendering Test")
    
    # Sample content similar to what agents produce
    sample_content = """
**Prioritized Course Recommendations for <PERSON> (ID: 1)**

1. **General Education Courses (Highest Priority - Need 15 credits)**
   - *Recommended:* 2-3 General Education courses (6-9 credits)
   - *Reasoning:* <PERSON> has made no progress on her General Education requirements, which are critical for graduation.

2. **Core Computer Science Courses (High Priority - Need 12 credits)**
   - *Recommended:* 1 Core CS course (4 credits)
   - *Specific Suggestions:* Based on her completed courses:
     - Database Systems (follow-up to Data Structures)
     - Operating Systems (natural progression from Computer Systems)

3. **Elective Courses (Medium Priority - Need 12 credits)**
   - *Recommended:* 1 CS Elective (3-4 credits) if schedule permits
   - *Specific Suggestions:* Given her interest and strong performance:
     - Advanced Machine Learning
     - Data Mining
     - Artificial Intelligence

**Balanced Semester Plan Recommendation:**
- 2 General Education courses (6-8 credits)
- 1 Core CS course (4 credits)
- (Optional) 1 CS Elective (3 credits) if total credits < 16

**Additional Recommendations:**
1. *Summer Term Consideration:* <PERSON> should consider taking 1-2 General Education courses during summer.
2. *Course Load Management:* Given her 3.7 GPA, she can handle a moderate course load.
3. *Long-term Planning:* She should aim to complete all General Education requirements within the next two semesters.
"""

    st.markdown("## Test 1: Direct Markdown Rendering")
    st.markdown(sample_content)
    
    st.markdown("---")
    
    st.markdown("## Test 2: With Success Box")
    st.success("✅ Personalized Course Suggestions Generated")
    st.markdown(sample_content)
    
    st.markdown("---")
    
    st.markdown("## Test 3: In Columns")
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.markdown("**Agent Info:**")
        st.markdown("- Role: Course Advisor")
        st.markdown("- Purpose: Generate recommendations")
        
    with col2:
        st.markdown("**Agent Output:**")
        st.markdown(sample_content)
    
    st.markdown("---")
    
    st.markdown("## Test 4: Button Toggle")
    if st.button("🔍 View Full Output"):
        st.markdown("**Full Agent Output:**")
        st.markdown(sample_content)

if __name__ == "__main__":
    test_markdown_content()
