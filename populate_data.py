"""
Populate the university database with sample data
"""

import sqlite3
import json

def populate_database():
    """Add sample data to the university database"""
    
    conn = sqlite3.connect("university.db")
    cursor = conn.cursor()
    
    # Sample students data
    students_data = [
        {
            "name": "<PERSON>",
            "gpa": 3.7,
            "interests": "Machine Learning, Data Science, Web Development",
            "major": "Computer Science",
            "year": 3
        },
        {
            "name": "Bob Chen",
            "gpa": 3.2,
            "interests": "Cybersecurity, Networks, Systems Programming",
            "major": "Computer Science", 
            "year": 2
        },
        {
            "name": "<PERSON>",
            "gpa": 3.9,
            "interests": "AI, Robotics, Computer Vision",
            "major": "Computer Science",
            "year": 4
        }
    ]
    
    # Insert students
    for student in students_data:
        cursor.execute("""
            INSERT INTO students (name, gpa, interests, major, year)
            VALUES (?, ?, ?, ?, ?)
        """, (student["name"], student["gpa"], student["interests"], 
              student["major"], student["year"]))
    
    # Sample courses data
    courses_data = [
        # Core CS courses
        {"name": "Data Structures and Algorithms", "credits": 4, "semester": "Fall", 
         "category": "Core", "description": "Fundamental data structures and algorithms"},
        {"name": "Computer Systems", "credits": 4, "semester": "Spring", 
         "category": "Core", "description": "Computer architecture and systems programming"},
        {"name": "Database Systems", "credits": 3, "semester": "Fall", 
         "category": "Core", "description": "Database design and implementation"},
        {"name": "Software Engineering", "credits": 3, "semester": "Spring", 
         "category": "Core", "description": "Software development methodologies"},
        {"name": "Operating Systems", "credits": 4, "semester": "Fall", 
         "category": "Core", "description": "OS concepts and implementation"},
        
        # Math requirements
        {"name": "Calculus I", "credits": 4, "semester": "Fall", 
         "category": "Math", "description": "Differential calculus"},
        {"name": "Discrete Mathematics", "credits": 3, "semester": "Spring", 
         "category": "Math", "description": "Logic, sets, and discrete structures"},
        {"name": "Statistics", "credits": 3, "semester": "Fall", 
         "category": "Math", "description": "Probability and statistics"},
        
        # Electives
        {"name": "Machine Learning", "credits": 3, "semester": "Spring", 
         "category": "Elective", "description": "ML algorithms and applications"},
        {"name": "Computer Graphics", "credits": 3, "semester": "Fall", 
         "category": "Elective", "description": "3D graphics and visualization"},
        {"name": "Cybersecurity", "credits": 3, "semester": "Spring", 
         "category": "Elective", "description": "Security principles and practices"},
        {"name": "Web Development", "credits": 3, "semester": "Fall", 
         "category": "Elective", "description": "Full-stack web development"},
        {"name": "Mobile App Development", "credits": 3, "semester": "Spring", 
         "category": "Elective", "description": "iOS and Android development"},
        {"name": "Artificial Intelligence", "credits": 3, "semester": "Fall", 
         "category": "Elective", "description": "AI concepts and techniques"},
        {"name": "Computer Vision", "credits": 3, "semester": "Spring", 
         "category": "Elective", "description": "Image processing and computer vision"}
    ]
    
    # Insert courses
    for course in courses_data:
        cursor.execute("""
            INSERT INTO courses (name, credits, semester, category, description)
            VALUES (?, ?, ?, ?, ?)
        """, (course["name"], course["credits"], course["semester"], 
              course["category"], course["description"]))
    
    # Sample degree requirements
    degree_requirements = [
        {"category": "Core", "required_credits": 20, 
         "description": "Core computer science courses"},
        {"category": "Math", "required_credits": 10, 
         "description": "Mathematics requirements"},
        {"category": "Elective", "required_credits": 15, 
         "description": "CS elective courses"},
        {"category": "General", "required_credits": 15, 
         "description": "General education requirements"}
    ]
    
    # Insert degree requirements
    for req in degree_requirements:
        cursor.execute("""
            INSERT INTO degree_requirements (category, required_credits, description, major)
            VALUES (?, ?, ?, ?)
        """, (req["category"], req["required_credits"], req["description"], "Computer Science"))
    
    conn.commit()
    print("Sample data inserted successfully!")
    
    return conn, cursor

def add_sample_enrollments(conn, cursor):
    """Add sample enrollments for students"""
    
    # Sample enrollments - Alice (student_id=1)
    alice_enrollments = [
        (1, 1, "A", "Fall", 2022),  # Data Structures
        (1, 2, "B+", "Spring", 2023),  # Computer Systems  
        (1, 6, "A", "Fall", 2022),  # Calculus I
        (1, 7, "A-", "Spring", 2023),  # Discrete Math
        (1, 8, "B+", "Fall", 2023),  # Statistics
        (1, 9, "A", "Spring", 2024),  # Machine Learning
    ]
    
    # Bob (student_id=2) 
    bob_enrollments = [
        (2, 1, "B", "Fall", 2023),  # Data Structures
        (2, 6, "B-", "Fall", 2023),  # Calculus I
        (2, 7, "B+", "Spring", 2024),  # Discrete Math
        (2, 11, "A-", "Spring", 2024),  # Cybersecurity
    ]
    
    # Carol (student_id=3)
    carol_enrollments = [
        (3, 1, "A", "Fall", 2021),  # Data Structures
        (3, 2, "A", "Spring", 2022),  # Computer Systems
        (3, 3, "A-", "Fall", 2022),  # Database Systems
        (3, 4, "A", "Spring", 2023),  # Software Engineering
        (3, 5, "A-", "Fall", 2023),  # Operating Systems
        (3, 6, "A", "Fall", 2021),  # Calculus I
        (3, 7, "A", "Spring", 2022),  # Discrete Math
        (3, 8, "A-", "Fall", 2022),  # Statistics
        (3, 9, "A", "Spring", 2023),  # Machine Learning
        (3, 14, "A", "Fall", 2023),  # Artificial Intelligence
        (3, 15, "A-", "Spring", 2024),  # Computer Vision
    ]
    
    all_enrollments = alice_enrollments + bob_enrollments + carol_enrollments
    
    for enrollment in all_enrollments:
        cursor.execute("""
            INSERT INTO enrollments (student_id, course_id, grade, semester_taken, year_taken)
            VALUES (?, ?, ?, ?, ?)
        """, enrollment)
    
    conn.commit()
    print("Sample enrollments added successfully!")

def verify_data():
    """Verify the inserted data"""
    conn = sqlite3.connect("university.db")
    cursor = conn.cursor()
    
    # Count records in each table
    tables = ["students", "courses", "enrollments", "degree_requirements"]
    
    print("\nData verification:")
    for table in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f"- {table}: {count} records")
    
    # Show sample student data
    print("\nSample students:")
    cursor.execute("SELECT id, name, gpa, major, year FROM students")
    students = cursor.fetchall()
    for student in students:
        print(f"  {student[0]}: {student[1]} (GPA: {student[2]}, Year: {student[4]})")
    
    # Show sample courses by category
    print("\nCourses by category:")
    cursor.execute("SELECT category, COUNT(*) FROM courses GROUP BY category")
    categories = cursor.fetchall()
    for category in categories:
        print(f"  {category[0]}: {category[1]} courses")
    
    conn.close()

if __name__ == "__main__":
    print("Populating database with sample data...")
    conn, cursor = populate_database()
    add_sample_enrollments(conn, cursor)
    conn.close()
    verify_data()
    print("\nDatabase population complete!")
