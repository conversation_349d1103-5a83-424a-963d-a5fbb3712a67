"""
AI Mentor: Personalized Academic & Career Guidance Assistant
Streamlit Web Application
"""

import streamlit as st
import sqlite3
import pandas as pd
import os
from agents import create_ai_mentor_crew
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Page configuration
st.set_page_config(
    page_title="AI Mentor - Academic Advisor",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .student-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.75rem;
        margin: 1rem 0;
        color: #212529;
        border: 2px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .student-card h3 {
        color: #1f77b4;
        margin-bottom: 0.75rem;
        font-size: 1.5rem;
    }
    .student-card p {
        color: #495057;
        margin: 0.5rem 0;
        font-size: 1rem;
        line-height: 1.4;
    }
    .student-card strong {
        color: #2c3e50;
        font-weight: 600;
    }

    /* Dark mode compatibility */
    @media (prefers-color-scheme: dark) {
        .student-card {
            background-color: #2d3748;
            color: #e2e8f0;
            border-color: #4a5568;
        }
        .student-card h3 {
            color: #63b3ed;
        }
        .student-card p {
            color: #cbd5e0;
        }
        .student-card strong {
            color: #90cdf4;
        }
    }

    /* Improve markdown table styling */
    .stMarkdown table {
        border-collapse: collapse;
        margin: 1rem 0;
        font-size: 0.9rem;
    }
    .stMarkdown table th,
    .stMarkdown table td {
        border: 1px solid #ddd;
        padding: 8px 12px;
        text-align: left;
    }
    .stMarkdown table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }
    .stMarkdown table tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    /* Improve markdown headers in results */
    .stMarkdown h1, .stMarkdown h2, .stMarkdown h3, .stMarkdown h4 {
        color: #1f77b4;
        margin-top: 1.5rem;
        margin-bottom: 0.5rem;
    }
    .stMarkdown h1 {
        border-bottom: 2px solid #1f77b4;
        padding-bottom: 0.3rem;
    }
    .stMarkdown h2 {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 0.2rem;
    }
    .metric-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        margin: 0.5rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .trace-step {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 0 0.5rem 0.5rem 0;
    }
    .agent-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 0.75rem;
        margin: 0.5rem 0;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .insight-item {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 0.75rem;
        margin: 0.25rem 0;
        border-radius: 0 0.25rem 0.25rem 0;
    }
    .action-item {
        background-color: #fff3e0;
        border-left: 4px solid #ff9800;
        padding: 0.75rem;
        margin: 0.25rem 0;
        border-radius: 0 0.25rem 0.25rem 0;
    }
</style>
""", unsafe_allow_html=True)

def load_students():
    """Load student data from database"""
    try:
        conn = sqlite3.connect("university.db")
        students_df = pd.read_sql_query(
            "SELECT id, name, gpa, major, year, interests FROM students ORDER BY name", 
            conn
        )
        conn.close()
        return students_df
    except Exception as e:
        st.error(f"Error loading students: {e}")
        return pd.DataFrame()

def get_student_summary(student_id):
    """Get student summary information"""
    try:
        conn = sqlite3.connect("university.db")
        
        # Get student info
        student_query = "SELECT * FROM students WHERE id = ?"
        student_df = pd.read_sql_query(student_query, conn, params=(student_id,))
        
        # Get enrollment count
        enrollment_query = "SELECT COUNT(*) as course_count FROM enrollments WHERE student_id = ?"
        enrollment_df = pd.read_sql_query(enrollment_query, conn, params=(student_id,))
        
        # Get total credits
        credits_query = """
            SELECT SUM(c.credits) as total_credits
            FROM enrollments e
            JOIN courses c ON e.course_id = c.id
            WHERE e.student_id = ?
        """
        credits_df = pd.read_sql_query(credits_query, conn, params=(student_id,))
        
        conn.close()
        
        if not student_df.empty:
            student = student_df.iloc[0]
            course_count = enrollment_df.iloc[0]['course_count']
            total_credits = credits_df.iloc[0]['total_credits'] or 0
            
            return {
                'student': student,
                'course_count': course_count,
                'total_credits': total_credits
            }
    except Exception as e:
        st.error(f"Error getting student summary: {e}")
    
    return None

def display_student_info(student_summary):
    """Display student information in a nice format"""
    if not student_summary:
        return
    
    student = student_summary['student']
    
    st.markdown(f"""
    <div class="student-card">
        <h3>👤 {student['name']}</h3>
        <p><strong>Major:</strong> {student['major']}</p>
        <p><strong>Year:</strong> {student['year']}</p>
        <p><strong>GPA:</strong> {student['gpa']}</p>
        <p><strong>Interests:</strong> {student['interests']}</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Courses Completed", student_summary['course_count'])
    with col2:
        st.metric("Total Credits", student_summary['total_credits'])
    with col3:
        st.metric("Current GPA", student['gpa'])

def check_api_configuration():
    """Check if API is configured"""
    api_key = os.getenv("DEEPSEEK_API_KEY")
    return api_key is not None

def display_analysis_results(result):
    """Display the main analysis results in a structured format"""
    if hasattr(result, 'tasks_output') and result.tasks_output:
        # Display each task output with enhanced formatting
        for i, task_output in enumerate(result.tasks_output):
            if i == 0:
                st.markdown("#### 👤 Student Profile Analysis")
                with st.expander("View Full Profile Analysis", expanded=True):
                    st.markdown(task_output.raw)
            elif i == 1:
                st.markdown("#### 📋 Degree Validation")
                with st.expander("View Degree Progress Details", expanded=True):
                    # Parse and highlight degree gaps
                    content = task_output.raw
                    if "⚠" in content or "Need" in content:
                        st.markdown('<div class="warning-box">', unsafe_allow_html=True)
                        st.markdown("**⚠️ Degree Requirements Gaps Found**")
                        st.markdown('</div>', unsafe_allow_html=True)

                    st.markdown(content)
            elif i == 2:
                st.markdown("#### 💡 Course Recommendations")
                with st.expander("View Recommended Courses", expanded=True):
                    content = task_output.raw
                    st.markdown('<div class="success-box">', unsafe_allow_html=True)
                    st.markdown("**✅ Personalized Course Suggestions**")
                    st.markdown('</div>', unsafe_allow_html=True)
                    st.markdown(content)

            if i < len(result.tasks_output) - 1:
                st.markdown("---")
    else:
        # Fallback display
        result_text = str(result)
        if result_text.startswith('**') or '##' in result_text or '|' in result_text:
            st.markdown(result_text)
        else:
            st.text(result_text)

def display_agent_trace(result):
    """Display detailed agent decision trace for explainability"""
    st.markdown("""
    This section shows the step-by-step decision-making process of each AI agent,
    providing transparency into how recommendations were generated.
    """)

    if hasattr(result, 'tasks_output') and result.tasks_output:
        # Agent information
        agent_info = [
            {
                "name": "Student Profile Agent",
                "role": "Data Analyst",
                "icon": "👤",
                "description": "Analyzes student academic history, GPA, interests, and current standing"
            },
            {
                "name": "Degree Validator Agent",
                "role": "Requirements Specialist",
                "icon": "📋",
                "description": "Validates progress against degree requirements and identifies gaps"
            },
            {
                "name": "Course Recommender Agent",
                "role": "Academic Advisor",
                "icon": "💡",
                "description": "Recommends optimal courses based on requirements and student interests"
            }
        ]

        for i, (task_output, agent) in enumerate(zip(result.tasks_output, agent_info)):
            with st.expander(f"{agent['icon']} {agent['name']} - Decision Process", expanded=False):

                # Agent role and description
                st.markdown(f"**Role:** {agent['role']}")
                st.markdown(f"**Purpose:** {agent['description']}")
                st.markdown("---")

                # Task execution details
                st.markdown("**🔄 Task Execution:**")

                # Show what tools/data the agent accessed
                if i == 0:  # Student Profile Agent
                    st.markdown("**Data Sources Accessed:**")
                    st.markdown("- ✅ Student basic information (name, GPA, major, year)")
                    st.markdown("- ✅ Complete enrollment history with grades")
                    st.markdown("- ✅ Academic performance metrics")

                elif i == 1:  # Degree Validator Agent
                    st.markdown("**Analysis Performed:**")
                    st.markdown("- ✅ Retrieved Computer Science degree requirements")
                    st.markdown("- ✅ Calculated earned credits by category")
                    st.markdown("- ✅ Identified requirement gaps and priorities")

                elif i == 2:  # Course Recommender Agent
                    st.markdown("**Recommendation Process:**")
                    st.markdown("- ✅ Analyzed available courses by category")
                    st.markdown("- ✅ Matched student interests with course options")
                    st.markdown("- ✅ Prioritized based on degree requirements")

                st.markdown("---")

                # Agent reasoning and output
                st.markdown("**🧠 Agent Reasoning & Output:**")

                # Parse the output to extract key insights
                content = task_output.raw

                # Try to extract structured information
                if "GPA:" in content and i == 0:
                    st.markdown("**Key Insights Identified:**")
                    lines = content.split('\n')
                    for line in lines:
                        if 'GPA:' in line or 'Major:' in line or 'Year:' in line:
                            st.markdown(f"- {line.strip()}")

                elif ("credits" in content.lower() or "requirement" in content.lower()) and i == 1:
                    st.markdown("**Degree Progress Assessment:**")
                    if "⚠" in content or "Need" in content:
                        st.warning("Gaps identified in degree requirements")
                    if "✓" in content or "Complete" in content:
                        st.success("Some requirements completed successfully")

                elif i == 2:
                    st.markdown("**Recommendation Strategy:**")
                    if "recommend" in content.lower():
                        st.info("Personalized course suggestions generated based on analysis")

                # Show full agent output in a code block for detailed inspection
                with st.expander("View Full Agent Output", expanded=False):
                    st.markdown("```")
                    st.text(content)
                    st.markdown("```")

                # Execution metadata if available
                if hasattr(task_output, 'agent') and task_output.agent:
                    st.markdown("**⚙️ Execution Details:**")
                    st.markdown(f"- Agent: {task_output.agent}")

                if hasattr(task_output, 'tools_used'):
                    st.markdown(f"- Tools Used: {', '.join(task_output.tools_used)}")
    else:
        st.warning("No detailed trace information available for this analysis.")

def display_executive_summary(result, student_id):
    """Display an executive summary with key metrics and actionable insights"""

    # Get student info for context
    student_summary = get_student_summary(student_id)
    if student_summary:
        student = student_summary['student']

        st.markdown(f"**Student:** {student['name']} | **Major:** {student['major']} | **GPA:** {student['gpa']}")
        st.markdown("---")

    # Summary metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            label="Analysis Agents",
            value="3",
            help="Number of AI agents that analyzed this student"
        )

    with col2:
        # Try to extract completion status from results
        completion_status = "In Progress"
        if hasattr(result, 'tasks_output') and result.tasks_output:
            for task_output in result.tasks_output:
                if "✓" in task_output.raw and "Complete" in task_output.raw:
                    completion_status = "On Track"
                elif "⚠" in task_output.raw or "Need" in task_output.raw:
                    completion_status = "Needs Attention"

        status_color = "normal"
        if completion_status == "On Track":
            status_color = "inverse"
        elif completion_status == "Needs Attention":
            status_color = "off"

        st.metric(
            label="Degree Status",
            value=completion_status,
            help="Overall assessment of degree completion progress"
        )

    with col3:
        # Count recommendations if available
        rec_count = 0
        if hasattr(result, 'tasks_output') and len(result.tasks_output) > 2:
            content = result.tasks_output[2].raw.lower()
            # Simple heuristic to count recommendations
            rec_count = content.count('recommend') + content.count('suggest') + content.count('course')
            rec_count = min(rec_count, 10)  # Cap at reasonable number

        st.metric(
            label="Recommendations",
            value=f"{rec_count}+",
            help="Number of course recommendations generated"
        )

    st.markdown("---")

    # Key insights extraction
    st.markdown("### 🎯 Key Insights")

    insights = []
    action_items = []

    if hasattr(result, 'tasks_output') and result.tasks_output:
        for i, task_output in enumerate(result.tasks_output):
            content = task_output.raw

            if i == 0:  # Profile analysis
                if "GPA" in content:
                    gpa_match = [line for line in content.split('\n') if 'GPA' in line]
                    if gpa_match:
                        insights.append(f"📊 {gpa_match[0].strip()}")

                if "Interest" in content:
                    interest_match = [line for line in content.split('\n') if 'Interest' in line]
                    if interest_match:
                        insights.append(f"🎯 {interest_match[0].strip()}")

            elif i == 1:  # Degree validation
                if "⚠" in content or "Need" in content:
                    gaps = [line.strip() for line in content.split('\n') if '⚠' in line or 'Need' in line]
                    for gap in gaps[:3]:  # Show top 3 gaps
                        action_items.append(f"🔴 {gap}")

                if "✓" in content or "Complete" in content:
                    completed = [line.strip() for line in content.split('\n') if '✓' in line]
                    for comp in completed[:2]:  # Show top 2 completions
                        insights.append(f"✅ {comp}")

            elif i == 2:  # Recommendations
                # Extract course names if mentioned
                lines = content.split('\n')
                course_lines = [line.strip() for line in lines if 'course' in line.lower() and len(line.strip()) > 10]
                for course in course_lines[:3]:  # Show top 3 recommendations
                    action_items.append(f"💡 {course}")

    # Display insights
    if insights:
        st.markdown("**Strengths & Current Status:**")
        for insight in insights:
            st.markdown(f"- {insight}")

    if action_items:
        st.markdown("**Action Items & Recommendations:**")
        for item in action_items:
            st.markdown(f"- {item}")

    if not insights and not action_items:
        st.info("Analysis completed. Review the detailed results in the other tabs for specific insights and recommendations.")

    # Quick actions
    st.markdown("---")
    st.markdown("### ⚡ Quick Actions")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📋 View Degree Plan", use_container_width=True):
            st.info("Feature coming soon: Interactive degree planning tool")

    with col2:
        if st.button("📚 Browse Courses", use_container_width=True):
            st.info("Feature coming soon: Course catalog browser")

    with col3:
        if st.button("📊 Export Report", use_container_width=True):
            st.info("Feature coming soon: PDF report generation")

def main():
    """Main application"""
    
    # Header
    st.markdown('<h1 class="main-header">🎓 AI Mentor - Academic Advisor</h1>', unsafe_allow_html=True)
    st.markdown("*Personalized academic guidance powered by AI*")
    
    # Sidebar
    st.sidebar.title("Navigation")
    
    # Load students
    students_df = load_students()
    
    if students_df.empty:
        st.error("No students found in database. Please run the database setup scripts first.")
        return
    
    # Student selection
    st.sidebar.subheader("Select Student")
    student_options = {f"{row['name']} (ID: {row['id']})": row['id'] 
                      for _, row in students_df.iterrows()}
    
    selected_student_key = st.sidebar.selectbox(
        "Choose a student:",
        options=list(student_options.keys())
    )
    
    selected_student_id = student_options[selected_student_key]
    
    # API Configuration Check
    st.sidebar.subheader("Configuration")
    api_configured = check_api_configuration()
    
    if api_configured:
        st.sidebar.success("✅ DeepSeek API configured")
    else:
        st.sidebar.warning("⚠️ DeepSeek API not configured")
        st.sidebar.info("Add DEEPSEEK_API_KEY to .env file for full functionality")
    
    # Main content
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("Student Profile")
        student_summary = get_student_summary(selected_student_id)
        display_student_info(student_summary)
        
        # Run AI Mentor button
        st.subheader("AI Analysis")
        
        if st.button("🤖 Run AI Mentor Analysis", type="primary", use_container_width=True):
            if not api_configured:
                st.error("Please configure your API key in the .env file to run AI analysis.")
            else:
                with st.spinner("Running AI analysis... This may take a few minutes."):
                    try:
                        # Create and run crew
                        crew = create_ai_mentor_crew(selected_student_id)
                        result = crew.kickoff()
                        
                        # Store result in session state
                        st.session_state.analysis_result = result
                        st.session_state.analyzed_student = selected_student_id
                        st.success("✅ Analysis complete!")
                        
                    except Exception as e:
                        st.error(f"Error running analysis: {e}")
    
    with col2:
        st.subheader("AI Mentor Results")

        # Display results if available
        if hasattr(st.session_state, 'analysis_result') and \
           hasattr(st.session_state, 'analyzed_student') and \
           st.session_state.analyzed_student == selected_student_id:

            # Create tabs for different views
            tab1, tab2, tab3 = st.tabs(["📊 Results", "🔍 Agent Trace", "📈 Summary"])

            result = st.session_state.analysis_result

            with tab1:
                st.markdown("### 📊 Analysis Results")
                display_analysis_results(result)

            with tab2:
                st.markdown("### 🔍 Agent Decision Trace")
                display_agent_trace(result)

            with tab3:
                st.markdown("### 📈 Executive Summary")
                display_executive_summary(result, selected_student_id)

        else:
            st.info("👆 Select a student and click 'Run AI Mentor Analysis' to see personalized recommendations.")

            # Show sample data while waiting
            st.markdown("### 📚 Available Courses Preview")
            try:
                conn = sqlite3.connect("university.db")
                courses_df = pd.read_sql_query(
                    "SELECT name, credits, category, semester FROM courses ORDER BY category, name LIMIT 10",
                    conn
                )
                conn.close()
                st.dataframe(courses_df, use_container_width=True)
            except Exception as e:
                st.error(f"Error loading courses: {e}")

if __name__ == "__main__":
    main()
