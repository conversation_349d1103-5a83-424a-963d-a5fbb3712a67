"""
AI Mentor: Personalized Academic & Career Guidance Assistant
Streamlit Web Application
"""

import streamlit as st
import sqlite3
import pandas as pd
import os
from agents import create_ai_mentor_crew
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Page configuration
st.set_page_config(
    page_title="AI Mentor - Academic Advisor",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .student-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.75rem;
        margin: 1rem 0;
        color: #212529;
        border: 2px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .student-card h3 {
        color: #1f77b4;
        margin-bottom: 0.75rem;
        font-size: 1.5rem;
    }
    .student-card p {
        color: #495057;
        margin: 0.5rem 0;
        font-size: 1rem;
        line-height: 1.4;
    }
    .student-card strong {
        color: #2c3e50;
        font-weight: 600;
    }

    /* Dark mode compatibility */
    @media (prefers-color-scheme: dark) {
        .student-card {
            background-color: #2d3748;
            color: #e2e8f0;
            border-color: #4a5568;
        }
        .student-card h3 {
            color: #63b3ed;
        }
        .student-card p {
            color: #cbd5e0;
        }
        .student-card strong {
            color: #90cdf4;
        }
    }

    /* Improve markdown table styling */
    .stMarkdown table {
        border-collapse: collapse;
        margin: 1rem 0;
        font-size: 0.9rem;
    }
    .stMarkdown table th,
    .stMarkdown table td {
        border: 1px solid #ddd;
        padding: 8px 12px;
        text-align: left;
    }
    .stMarkdown table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }
    .stMarkdown table tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    /* Improve markdown headers in results */
    .stMarkdown h1, .stMarkdown h2, .stMarkdown h3, .stMarkdown h4 {
        color: #1f77b4;
        margin-top: 1.5rem;
        margin-bottom: 0.5rem;
    }
    .stMarkdown h1 {
        border-bottom: 2px solid #1f77b4;
        padding-bottom: 0.3rem;
    }
    .stMarkdown h2 {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 0.2rem;
    }
    .metric-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        margin: 0.5rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def load_students():
    """Load student data from database"""
    try:
        conn = sqlite3.connect("university.db")
        students_df = pd.read_sql_query(
            "SELECT id, name, gpa, major, year, interests FROM students ORDER BY name", 
            conn
        )
        conn.close()
        return students_df
    except Exception as e:
        st.error(f"Error loading students: {e}")
        return pd.DataFrame()

def get_student_summary(student_id):
    """Get student summary information"""
    try:
        conn = sqlite3.connect("university.db")
        
        # Get student info
        student_query = "SELECT * FROM students WHERE id = ?"
        student_df = pd.read_sql_query(student_query, conn, params=(student_id,))
        
        # Get enrollment count
        enrollment_query = "SELECT COUNT(*) as course_count FROM enrollments WHERE student_id = ?"
        enrollment_df = pd.read_sql_query(enrollment_query, conn, params=(student_id,))
        
        # Get total credits
        credits_query = """
            SELECT SUM(c.credits) as total_credits
            FROM enrollments e
            JOIN courses c ON e.course_id = c.id
            WHERE e.student_id = ?
        """
        credits_df = pd.read_sql_query(credits_query, conn, params=(student_id,))
        
        conn.close()
        
        if not student_df.empty:
            student = student_df.iloc[0]
            course_count = enrollment_df.iloc[0]['course_count']
            total_credits = credits_df.iloc[0]['total_credits'] or 0
            
            return {
                'student': student,
                'course_count': course_count,
                'total_credits': total_credits
            }
    except Exception as e:
        st.error(f"Error getting student summary: {e}")
    
    return None

def display_student_info(student_summary):
    """Display student information in a nice format"""
    if not student_summary:
        return
    
    student = student_summary['student']
    
    st.markdown(f"""
    <div class="student-card">
        <h3>👤 {student['name']}</h3>
        <p><strong>Major:</strong> {student['major']}</p>
        <p><strong>Year:</strong> {student['year']}</p>
        <p><strong>GPA:</strong> {student['gpa']}</p>
        <p><strong>Interests:</strong> {student['interests']}</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Courses Completed", student_summary['course_count'])
    with col2:
        st.metric("Total Credits", student_summary['total_credits'])
    with col3:
        st.metric("Current GPA", student['gpa'])

def check_api_configuration():
    """Check if API is configured"""
    api_key = os.getenv("DEEPSEEK_API_KEY")
    return api_key is not None

def main():
    """Main application"""
    
    # Header
    st.markdown('<h1 class="main-header">🎓 AI Mentor - Academic Advisor</h1>', unsafe_allow_html=True)
    st.markdown("*Personalized academic guidance powered by AI*")
    
    # Sidebar
    st.sidebar.title("Navigation")
    
    # Load students
    students_df = load_students()
    
    if students_df.empty:
        st.error("No students found in database. Please run the database setup scripts first.")
        return
    
    # Student selection
    st.sidebar.subheader("Select Student")
    student_options = {f"{row['name']} (ID: {row['id']})": row['id'] 
                      for _, row in students_df.iterrows()}
    
    selected_student_key = st.sidebar.selectbox(
        "Choose a student:",
        options=list(student_options.keys())
    )
    
    selected_student_id = student_options[selected_student_key]
    
    # API Configuration Check
    st.sidebar.subheader("Configuration")
    api_configured = check_api_configuration()
    
    if api_configured:
        st.sidebar.success("✅ DeepSeek API configured")
    else:
        st.sidebar.warning("⚠️ DeepSeek API not configured")
        st.sidebar.info("Add DEEPSEEK_API_KEY to .env file for full functionality")
    
    # Main content
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("Student Profile")
        student_summary = get_student_summary(selected_student_id)
        display_student_info(student_summary)
        
        # Run AI Mentor button
        st.subheader("AI Analysis")
        
        if st.button("🤖 Run AI Mentor Analysis", type="primary", use_container_width=True):
            if not api_configured:
                st.error("Please configure your API key in the .env file to run AI analysis.")
            else:
                with st.spinner("Running AI analysis... This may take a few minutes."):
                    try:
                        # Create and run crew
                        crew = create_ai_mentor_crew(selected_student_id)
                        result = crew.kickoff()
                        
                        # Store result in session state
                        st.session_state.analysis_result = result
                        st.session_state.analyzed_student = selected_student_id
                        st.success("✅ Analysis complete!")
                        
                    except Exception as e:
                        st.error(f"Error running analysis: {e}")
    
    with col2:
        st.subheader("AI Mentor Results")
        
        # Display results if available
        if hasattr(st.session_state, 'analysis_result') and \
           hasattr(st.session_state, 'analyzed_student') and \
           st.session_state.analyzed_student == selected_student_id:
            
            st.markdown("### 📊 Analysis Results")
            
            # Display the crew results
            result = st.session_state.analysis_result
            
            if hasattr(result, 'tasks_output') and result.tasks_output:
                # Display each task output
                for i, task_output in enumerate(result.tasks_output):
                    if i == 0:
                        st.markdown("#### 👤 Student Profile Analysis")
                    elif i == 1:
                        st.markdown("#### 📋 Degree Validation")
                    elif i == 2:
                        st.markdown("#### 💡 Course Recommendations")

                    # Render markdown properly instead of code block
                    st.markdown(task_output.raw)
                    st.markdown("---")
            else:
                # For fallback, still render as markdown if possible
                result_text = str(result)
                if result_text.startswith('**') or '##' in result_text or '|' in result_text:
                    st.markdown(result_text)
                else:
                    st.text(result_text)
                
        else:
            st.info("👆 Select a student and click 'Run AI Mentor Analysis' to see personalized recommendations.")
            
            # Show sample data while waiting
            st.markdown("### 📚 Available Courses Preview")
            try:
                conn = sqlite3.connect("university.db")
                courses_df = pd.read_sql_query(
                    "SELECT name, credits, category, semester FROM courses ORDER BY category, name LIMIT 10", 
                    conn
                )
                conn.close()
                st.dataframe(courses_df, use_container_width=True)
            except Exception as e:
                st.error(f"Error loading courses: {e}")

if __name__ == "__main__":
    main()
