# AI Mentor: Personalized Academic & Career Guidance Assistant – POC Plan

## Overview

This Proof-of-Concept (POC) demonstrates a multi-agent system that acts as an AI Academic Advisor using:

- **Crew AI** for agent orchestration  
- **SQLite** as the student/course database  
- **Streamlit** for interactive UI  

---

## POC Goal

- Ingest a student profile from a SQLite database  
- Use Crew agents to:
  - Validate degree completion
  - Recommend elective courses
- Display results in Streamlit
- Trace agent decisions to show explainability

---

## Phase-Wise Implementation Plan

---

### Phase 1: Foundation Setup (Day 1–2)

**Objectives:**

- Setup project structure and initial DB schema  
- Define basic Crew agent scaffolding

**Tasks:**

- Initialize Python environment, install dependencies:
  - `crewai`, `streamlit`, `sqlite3`, `pandas`
- Create SQLite DB (`university.db`) with tables:
  - `students (id, name, gpa, interests)`
  - `courses (id, name, credits, semester, category)`
  - `enrollments (student_id, course_id)`
  - `degree_requirements (category, required_credits)`
- Add sample data (2–3 students, 10+ courses)
- Create base Crew agents:
  - `StudentProfileAgent`
  - `DegreeValidatorAgent`
  - `CourseRecommenderAgent`

**Output:**

- SQLite database  
- Crew agent definitions  
- Streamlit app starter file  

---

### Phase 2: Agent Logic & Data Pipeline (Day 3–5)

**Objectives:**

- Implement agent behavior  
- Connect SQLite to agent workflows  
- Build agent reasoning logic

**Tasks:**

- `StudentProfileAgent`: retrieves student GPA, interests, history  
- `DegreeValidatorAgent`: calculates earned vs. required credits by category  
- `CourseRecommenderAgent`: recommends electives based on interests, missing credits
- Set up Crew flow:
  - `StudentProfile → DegreeValidator → CourseRecommender`
- Ensure agents return logs and results

**Output:**

- Functional multi-agent workflow  
- Console logs for reasoning trace  
- Modular Crew pipeline

---

### Phase 3: Streamlit UI Integration (Day 6–7)

**Objectives:**

- Build user interface to trigger and view results  
- Display structured outputs

**Tasks:**

- Student selection dropdown  
- Button to "Run AI Mentor"
- Panels to show:
  - Student details
  - Degree gaps (highlighted in red)
  - Suggested courses (green with reasons)
- Optional: agent trace viewer panel

**Output:**

- Functional Streamlit UI  
- Connected to Crew backend  
- End-to-end user flow

---

### Phase 4: Feedback & Wrap-Up (Day 8–9)

**Objectives:**

- Polish logic and finalize POC  
- Prepare for demonstration

**Tasks:**

- Improve error handling (e.g., no courses enrolled)  
- Add tooltips, labels, and agent reasoning outputs  
- Test with multiple student profiles  

**Output:**

- Ready-to-demo POC  
- Codebase organized and documented  
- Preview video/screenshots (optional)

---

## Crew Agent Flow Diagram


\[StudentProfileAgent]
↓
\[DegreeValidatorAgent] → flags missing core/electives
↓
\[CourseRecommenderAgent] → suggests best-fit electives


---

## Tools & Tech

| Component     | Technology      |
|---------------|-----------------|
| Agent System  | Crew AI          |
| Database      | SQLite           |
| UI Framework  | Streamlit        |
| LLM           | Deepseek         |

---

## Success Criteria

- [ ] Student profiles can be selected
- [ ] Degree progress is validated
- [ ] Personalized course suggestions are made
- [ ] Full multi-agent pipeline is visible
- [ ] Streamlit displays results clearly

---
