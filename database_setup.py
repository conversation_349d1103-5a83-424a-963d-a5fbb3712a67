"""
Database setup script for AI Mentor POC
Creates SQLite database with required tables for student academic tracking
"""

import sqlite3
import os
from pathlib import Path

def create_database():
    """Create the university database with all required tables"""
    
    # Database path
    db_path = "university.db"
    
    # Remove existing database if it exists
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"Removed existing database: {db_path}")
    
    # Create connection
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create students table
    cursor.execute("""
        CREATE TABLE students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            gpa REAL NOT NULL,
            interests TEXT,
            major TEXT,
            year INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Create courses table
    cursor.execute("""
        CREATE TABLE courses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            credits INTEGER NOT NULL,
            semester TEXT NOT NULL,
            category TEXT NOT NULL,
            description TEXT,
            prerequisites TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Create enrollments table (junction table for students and courses)
    cursor.execute("""
        CREATE TABLE enrollments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            course_id INTEGER NOT NULL,
            grade TEXT,
            semester_taken TEXT,
            year_taken INTEGER,
            status TEXT DEFAULT 'completed',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id),
            FOREIGN KEY (course_id) REFERENCES courses (id),
            UNIQUE(student_id, course_id)
        )
    """)
    
    # Create degree_requirements table
    cursor.execute("""
        CREATE TABLE degree_requirements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            required_credits INTEGER NOT NULL,
            description TEXT,
            major TEXT DEFAULT 'Computer Science',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Commit changes
    conn.commit()
    
    print("Database tables created successfully!")
    print("Tables created:")
    print("- students")
    print("- courses") 
    print("- enrollments")
    print("- degree_requirements")
    
    # Close connection
    conn.close()
    
    return db_path

def verify_database():
    """Verify that all tables were created correctly"""
    conn = sqlite3.connect("university.db")
    cursor = conn.cursor()
    
    # Get list of tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print("\nDatabase verification:")
    print(f"Found {len(tables)} tables:")
    for table in tables:
        print(f"- {table[0]}")
        
        # Get table schema
        cursor.execute(f"PRAGMA table_info({table[0]});")
        columns = cursor.fetchall()
        print(f"  Columns: {len(columns)}")
        for col in columns:
            print(f"    {col[1]} ({col[2]})")
    
    conn.close()

if __name__ == "__main__":
    print("Setting up AI Mentor database...")
    db_path = create_database()
    verify_database()
    print(f"\nDatabase setup complete! Database file: {db_path}")
