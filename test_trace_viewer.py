"""
Test script for the enhanced trace viewer functionality
Tests the Streamlit app with mock data to verify trace display
"""

import streamlit as st
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_trace_viewer():
    """Test the trace viewer with mock data"""
    print("=== Testing Enhanced Trace Viewer ===\n")
    
    # Mock result object to simulate crew output
    class MockTaskOutput:
        def __init__(self, raw_content, agent_name):
            self.raw = raw_content
            self.agent = agent_name
    
    class MockResult:
        def __init__(self):
            self.tasks_output = [
                MockTaskOutput("""
Student Profile:
- ID: 1
- Name: <PERSON>
- GPA: 3.7
- Major: Computer Science
- Year: Junior
- Interests: Machine Learning, Web Development

Academic History (8 courses completed):
- Introduction to Programming (3 credits, Core) - Grade: A (Fall 2022)
- Data Structures (3 credits, Core) - Grade: B+ (Spring 2023)
- Calculus I (4 credits, Math) - Grade: B (Fall 2022)
- Linear Algebra (3 credits, Math) - Grade: A- (Spring 2023)
                """, "Student Profile Agent"),
                
                MockTaskOutput("""
Credits Analysis:
- Core: 15/18 credits ⚠ Need 3 more credits
- Math: 12/12 credits ✓ Complete
- Elective: 6/12 credits ⚠ Need 6 more credits
- General: 8/8 credits ✓ Complete

Priority areas that need immediate attention:
1. Core Computer Science courses - missing 3 credits
2. Elective courses - significant gap of 6 credits
                """, "Degree Validator Agent"),
                
                MockTaskOutput("""
Based on the analysis, I recommend the following courses for Alice:

**High Priority (Core Requirements):**
1. Database Systems (3 credits) - Aligns with web development interests
2. Software Engineering (3 credits) - Essential for career preparation

**Recommended Electives:**
1. Machine Learning Fundamentals (3 credits) - Matches stated interests
2. Web Development Advanced (3 credits) - Builds on interests
3. Data Mining (3 credits) - Complements ML focus

These recommendations address the critical 3-credit gap in core requirements while building toward Alice's career interests in machine learning and web development.
                """, "Course Recommender Agent")
            ]
    
    # Test the display functions
    try:
        from app import display_analysis_results, display_agent_trace, display_executive_summary
        
        mock_result = MockResult()
        
        print("✓ Successfully imported display functions")
        print("✓ Created mock result data")
        print("✓ Mock data includes 3 agent outputs with realistic content")
        print("\nMock data structure:")
        print(f"  - Tasks: {len(mock_result.tasks_output)}")
        for i, task in enumerate(mock_result.tasks_output):
            print(f"  - Task {i+1}: {task.agent} ({len(task.raw)} characters)")
        
        print("\n✅ Trace viewer test data prepared successfully!")
        print("\nTo test the full trace viewer:")
        print("1. Run: streamlit run app.py")
        print("2. Select a student")
        print("3. Click 'Run AI Mentor Analysis'")
        print("4. Check the 'Agent Trace' tab for detailed decision process")
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Make sure app.py is in the same directory")
    except Exception as e:
        print(f"✗ Error: {e}")

def test_database_for_trace():
    """Test database connectivity for trace functionality"""
    print("\n=== Testing Database for Trace Features ===\n")
    
    try:
        import sqlite3
        import pandas as pd
        
        conn = sqlite3.connect("university.db")
        
        # Test student data
        students = pd.read_sql_query("SELECT COUNT(*) as count FROM students", conn)
        print(f"✓ Students in database: {students.iloc[0]['count']}")
        
        # Test courses data  
        courses = pd.read_sql_query("SELECT COUNT(*) as count FROM courses", conn)
        print(f"✓ Courses in database: {courses.iloc[0]['count']}")
        
        # Test enrollments
        enrollments = pd.read_sql_query("SELECT COUNT(*) as count FROM enrollments", conn)
        print(f"✓ Enrollments in database: {enrollments.iloc[0]['count']}")
        
        conn.close()
        print("✅ Database ready for trace viewer functionality!")
        
    except Exception as e:
        print(f"✗ Database error: {e}")
        print("Run database_setup.py and populate_data.py first")

if __name__ == "__main__":
    print("Enhanced Trace Viewer Test")
    print("=" * 50)
    
    test_trace_viewer()
    test_database_for_trace()
    
    print("\n" + "=" * 50)
    print("Test complete!")
    print("\nNext steps:")
    print("1. Start the Streamlit app: streamlit run app.py")
    print("2. Test the new trace viewer tabs")
    print("3. Verify agent decision transparency")
