"""
AI Mentor Crew Agents
Defines the core agents for the AI Academic Advisor system
"""

from crewai import Agent, Task, Crew, LLM
from crewai.tools import tool
import sqlite3
import pandas as pd
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

# Configure logging for agent trace
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Configure DeepSeek LLM
def get_llm():
    """Get configured LLM instance"""
    api_key = os.getenv("DEEPSEEK_API_KEY")
    model = os.getenv("MODEL", "deepseek/deepseek-chat")  # LiteLLM format with deepseek/ prefix
    temperature = float(os.getenv("TEMPERATURE", "0.7"))
    max_tokens = int(os.getenv("MAX_TOKENS", "2000"))
    timeout = int(os.getenv("TIMEOUT", "120"))

    if not api_key:
        print("Warning: DEEPSEEK_API_KEY not found in environment variables")
        print("Please set up your .env file with DeepSeek API credentials")
        # Fallback to a basic configuration for development
        return LLM(model="gpt-3.5-turbo", temperature=0.7)

    # Set the environment variable for LiteLLM to use
    os.environ["DEEPSEEK_API_KEY"] = api_key

    return LLM(
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        timeout=timeout
    )

# Database tools
@tool
def get_student_profile(student_id: int) -> str:
    """Get comprehensive student profile including GPA, interests, and academic history"""
    logger.info(f"🔍 Student Profile Agent: Retrieving profile for student ID {student_id}")
    conn = sqlite3.connect("university.db")
    
    # Get student basic info
    student_query = """
        SELECT id, name, gpa, interests, major, year 
        FROM students 
        WHERE id = ?
    """
    student_df = pd.read_sql_query(student_query, conn, params=(student_id,))
    
    if student_df.empty:
        conn.close()
        logger.warning(f"❌ Student Profile Agent: Student ID {student_id} not found")
        return f"Student with ID {student_id} not found"
    
    student = student_df.iloc[0]
    
    # Get enrollment history
    enrollment_query = """
        SELECT c.name, c.credits, c.category, e.grade, e.semester_taken, e.year_taken
        FROM enrollments e
        JOIN courses c ON e.course_id = c.id
        WHERE e.student_id = ?
        ORDER BY e.year_taken, e.semester_taken
    """
    enrollments_df = pd.read_sql_query(enrollment_query, conn, params=(student_id,))
    
    conn.close()

    logger.info(f"✅ Student Profile Agent: Retrieved profile for {student['name']} with {len(enrollments_df)} courses")

    # Format response
    profile = f"""
Student Profile:
- ID: {student['id']}
- Name: {student['name']}
- GPA: {student['gpa']}
- Major: {student['major']}
- Year: {student['year']}
- Interests: {student['interests']}

Academic History ({len(enrollments_df)} courses completed):
"""
    
    for _, enrollment in enrollments_df.iterrows():
        profile += f"- {enrollment['name']} ({enrollment['credits']} credits, {enrollment['category']}) - Grade: {enrollment['grade']} ({enrollment['semester_taken']} {enrollment['year_taken']})\n"
    
    return profile

@tool
def get_degree_requirements() -> str:
    """Get degree requirements for Computer Science major"""
    conn = sqlite3.connect("university.db")
    
    requirements_query = """
        SELECT category, required_credits, description
        FROM degree_requirements
        WHERE major = 'Computer Science'
        ORDER BY category
    """
    requirements_df = pd.read_sql_query(requirements_query, conn)
    conn.close()
    
    result = "Computer Science Degree Requirements:\n"
    for _, req in requirements_df.iterrows():
        result += f"- {req['category']}: {req['required_credits']} credits ({req['description']})\n"
    
    return result

@tool
def calculate_credits_by_category(student_id: int) -> str:
    """Calculate earned credits by category for a student"""
    logger.info(f"📊 Degree Validator Agent: Calculating credits by category for student {student_id}")
    conn = sqlite3.connect("university.db")
    
    credits_query = """
        SELECT c.category, SUM(c.credits) as earned_credits
        FROM enrollments e
        JOIN courses c ON e.course_id = c.id
        WHERE e.student_id = ? AND e.status = 'completed'
        GROUP BY c.category
    """
    credits_df = pd.read_sql_query(credits_query, conn, params=(student_id,))
    
    # Get requirements
    requirements_query = """
        SELECT category, required_credits
        FROM degree_requirements
        WHERE major = 'Computer Science'
    """
    requirements_df = pd.read_sql_query(requirements_query, conn)
    conn.close()
    
    # Merge and calculate gaps
    result = "Credits Analysis:\n"
    
    for _, req in requirements_df.iterrows():
        category = req['category']
        required = req['required_credits']
        
        earned_row = credits_df[credits_df['category'] == category]
        earned = earned_row['earned_credits'].iloc[0] if not earned_row.empty else 0
        
        gap = max(0, required - earned)
        status = "✓ Complete" if gap == 0 else f"⚠ Need {gap} more credits"
        
        result += f"- {category}: {earned}/{required} credits {status}\n"
    
    return result

@tool
def get_available_courses(category: str = None) -> str:
    """Get available courses, optionally filtered by category"""
    conn = sqlite3.connect("university.db")
    
    if category:
        query = """
            SELECT name, credits, category, description, semester
            FROM courses
            WHERE category = ?
            ORDER BY name
        """
        courses_df = pd.read_sql_query(query, conn, params=(category,))
    else:
        query = """
            SELECT name, credits, category, description, semester
            FROM courses
            ORDER BY category, name
        """
        courses_df = pd.read_sql_query(query, conn)
    
    conn.close()
    
    if courses_df.empty:
        return f"No courses found for category: {category}" if category else "No courses found"
    
    result = f"Available Courses ({category or 'All Categories'}):\n"
    for _, course in courses_df.iterrows():
        result += f"- {course['name']} ({course['credits']} credits, {course['category']}) - {course['semester']} semester\n"
        result += f"  Description: {course['description']}\n"
    
    return result

# Agent Definitions
def create_student_profile_agent():
    """Create the Student Profile Agent"""
    return Agent(
        role="Student Profile Analyst",
        goal="Retrieve and analyze comprehensive student academic profiles including GPA, interests, course history, and academic standing",
        backstory="""You are an experienced academic advisor with deep knowledge of student records and academic tracking.
        You excel at gathering and presenting student information in a clear, comprehensive manner that helps other agents
        make informed decisions about academic planning.""",
        tools=[get_student_profile],
        llm=get_llm(),
        verbose=True,
        allow_delegation=False
    )

def create_degree_validator_agent():
    """Create the Degree Validation Agent"""
    return Agent(
        role="Degree Requirements Validator",
        goal="Analyze student progress against degree requirements and identify gaps in core, math, and elective credits",
        backstory="""You are a meticulous academic requirements specialist who ensures students meet all graduation criteria.
        You have extensive knowledge of Computer Science degree requirements and can quickly identify which requirements
        are complete and which need attention. You provide clear, actionable feedback on academic progress.""",
        tools=[get_degree_requirements, calculate_credits_by_category],
        llm=get_llm(),
        verbose=True,
        allow_delegation=False
    )

def create_course_recommender_agent():
    """Create the Course Recommendation Agent"""
    return Agent(
        role="Personalized Course Advisor",
        goal="Recommend optimal courses based on student interests, missing requirements, and academic goals",
        backstory="""You are an expert academic advisor who specializes in course selection and academic planning.
        You understand how to match student interests with degree requirements and can recommend courses that both
        fulfill requirements and align with career goals. You consider factors like course difficulty, prerequisites,
        and student academic performance when making recommendations.""",
        tools=[get_available_courses, calculate_credits_by_category],
        llm=get_llm(),
        verbose=True,
        allow_delegation=False
    )

# Task Definitions
def create_profile_analysis_task(student_id: int):
    """Create task for student profile analysis"""
    return Task(
        description=f"""
        Analyze the complete academic profile for student ID {student_id}.

        Your analysis should include:
        1. Student basic information (name, GPA, year, major, interests)
        2. Complete course history with grades and credits
        3. Academic performance summary
        4. Key strengths and areas for improvement

        Provide a comprehensive overview that will help other agents understand
        the student's academic standing and background.
        """,
        expected_output="A detailed student profile analysis including academic history, performance metrics, and key insights",
        agent=create_student_profile_agent()
    )

def create_degree_validation_task(student_id: int):
    """Create task for degree requirement validation"""
    return Task(
        description=f"""
        Validate degree progress for student ID {student_id} against Computer Science requirements.

        Your validation should include:
        1. Current progress in each requirement category (Core, Math, Elective, General)
        2. Specific credit gaps that need to be filled
        3. Priority areas that need immediate attention
        4. Overall graduation readiness assessment

        Be specific about which requirements are complete and which need work.
        """,
        expected_output="A comprehensive degree validation report showing progress, gaps, and priorities for graduation",
        agent=create_degree_validator_agent()
    )

def create_course_recommendation_task(student_id: int):
    """Create task for course recommendations"""
    return Task(
        description=f"""
        Based on the student profile and degree validation results, recommend optimal courses for student ID {student_id}.

        Your recommendations should:
        1. Address the most critical degree requirement gaps first
        2. Align with the student's stated interests and career goals
        3. Consider the student's academic performance and GPA
        4. Suggest a balanced course load for the next semester
        5. Provide reasoning for each recommendation

        Focus on courses that will help the student progress toward graduation while
        maintaining academic success and pursuing their interests.
        """,
        expected_output="A prioritized list of course recommendations with detailed reasoning for each suggestion",
        agent=create_course_recommender_agent()
    )

# Crew Creation
def create_ai_mentor_crew(student_id: int):
    """Create the complete AI Mentor crew for a specific student"""

    # Create tasks
    profile_task = create_profile_analysis_task(student_id)
    validation_task = create_degree_validation_task(student_id)
    recommendation_task = create_course_recommendation_task(student_id)

    # Create crew
    crew = Crew(
        agents=[
            create_student_profile_agent(),
            create_degree_validator_agent(),
            create_course_recommender_agent()
        ],
        tasks=[profile_task, validation_task, recommendation_task],
        verbose=True
    )

    return crew
