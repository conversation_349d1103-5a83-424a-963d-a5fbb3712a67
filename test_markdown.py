"""
Test markdown rendering in Streamlit
"""

import streamlit as st

def test_markdown_rendering():
    """Test various markdown elements"""
    
    st.title("Markdown Rendering Test")
    
    # Test sample AI output similar to what we get from agents
    sample_output = """
**Comprehensive Student Profile Analysis for <PERSON> (ID: 1)**

**1. Basic Information:**
- **Name:** <PERSON>
- **GPA:** 3.7 (Strong academic performance)
- **Major:** Computer Science
- **Year:** 3 (Junior)
- **Interests:** Machine Learning, Data Science, Web Development

**2. Complete Course History:**

| Course Name                     | Credits | Type     | Grade | Term        |
|---------------------------------|---------|----------|-------|-------------|
| Data Structures and Algorithms  | 4       | Core     | A     | Fall 2022   |
| Calculus I                      | 4       | Math     | A     | Fall 2022   |
| Statistics                      | 3       | Math     | B+    | Fall 2023   |
| Computer Systems                | 4       | Core     | B+    | Spring 2023 |
| Discrete Mathematics            | 3       | Math     | A-    | Spring 2023 |
| Machine Learning                | 3       | Elective | A     | Spring 2024 |

**3. Academic Performance Summary:**
- **Overall Performance:** Excellent (3.7 GPA)
- **Core Courses:** Strong performance
- **Math Foundation:** Consistently good
- **Electives:** Excelling in chosen specialization

**4. Key Strengths:**
1. Strong technical aptitude
2. Excellent performance in advanced topics
3. Solid mathematical foundation
4. Consistent academic performance
5. Clear focus in technical electives

**5. Areas for Improvement:**
1. Slightly lower performance in Computer Systems
2. Could benefit from more breadth in electives

### Recommendations:
- Continue pursuing Machine Learning/Data Science track
- Consider additional systems courses
- Explore interdisciplinary applications
"""
    
    st.markdown("## Sample AI Agent Output (Properly Rendered)")
    st.markdown(sample_output)
    
    st.markdown("---")
    
    st.markdown("## Same Content in Code Block (How it was before)")
    st.markdown(f"```\n{sample_output}\n```")

if __name__ == "__main__":
    test_markdown_rendering()
